# Plán vytvo<PERSON><PERSON><PERSON> hry Had (Snake Game)

## Přehled projektu
Vytvoření klasické hry Had v HTML/CSS/JavaScript, kde hráč ovlád<PERSON> hada, který se pohybuje po herní ploše a sbír<PERSON> jídlo.

## Technické požadavky
- HTML5 Canvas pro vykreslování
- JavaScript pro herní logiku
- CSS pro styling
- Responzivní design

## Detailní plán implementace

### 1. Z<PERSON><PERSON><PERSON><PERSON> struktura (HTML)
- [x] Vytvořit index.html
- [x] Přidat HTML5 Canvas element
- [x] Přidat ovládací prvky (tlačítka, skóre)
- [x] Přidat základní meta tagy

### 2. Styling (CSS)
- [x] Vytvořit styles.css
- [x] Nastylovat herní plochu (canvas)
- [x] Přidat styling pro UI prvky
- [x] Implementovat responzivní design
- [x] Přidat animace a přechody

### 3. <PERSON><PERSON><PERSON> log<PERSON> (JavaScript)
- [x] Vytvořit game.js
- [x] Implementovat základní herní smyčku
- [x] Vytvořit třídu/objekt pro hada
- [x] Implementovat pohyb hada
- [x] Přidat detekci kolizí
- [x] Implementovat generování jídla
- [x] Přidat systém skórování

### 4. Ovládání
- [x] Implementovat ovládání klávesnicí (šipky/WASD)
- [x] Přidat touch ovládání pro mobily
- [x] Implementovat pauzu hry
- [x] Přidat restart funkci

### 5. Herní mechaniky
- [x] Růst hada po snězení jídla
- [x] Zvyšování rychlosti s rostoucím skóre
- [x] Detekce kolize se stěnami
- [x] Detekce kolize se sebou samým
- [x] Game Over obrazovka

### 6. UI/UX vylepšení
- [x] Zobrazení aktuálního skóre
- [x] Zobrazení nejvyššího skóre (localStorage)
- [x] Start obrazovka
- [x] Instrukce pro hráče
- [ ] Zvukové efekty (volitelné)

### 7. Optimalizace a testování
- [x] Testování na různých zařízeních
- [x] Optimalizace výkonu
- [x] Ladění herní rychlosti
- [x] Kontrola responzivity

## Struktura souborů
```
Had/
├── index.html          # Hlavní HTML soubor
├── css/
│   └── styles.css      # Styling
├── js/
│   └── game.js         # Herní logika
└── tasks.md           # Tento soubor s plánem
```

## Herní parametry
- Velikost herní plochy: 400x400px (20x20 buněk po 20px)
- Počáteční rychlost: 150ms mezi pohyby
- Zrychlení: -5ms za každých 5 bodů
- Minimální rychlost: 50ms

## Priorita implementace
1. **Vysoká**: Základní pohyb hada, jídlo, kolize
2. **Střední**: UI, skórování, restart
3. **Nízká**: Zvuky, animace, pokročilé funkce

## Poznámky
- Použít requestAnimationFrame pro plynulé animace
- Implementovat localStorage pro ukládání high score
- Zajistit kompatibilitu s moderními prohlížeči
- Možnost rozšíření o více úrovní nebo power-upy v budoucnu

## ✅ PROJEKT DOKONČEN!

Hra had byla úspěšně implementována podle plánu. Všechny hlavní funkce jsou hotové:

### Implementované funkce:
- ✅ Plně funkční hra had s klasickými pravidly
- ✅ Responzivní design pro desktop i mobil
- ✅ Ovládání klávesnicí (šipky/WASD) i dotykem
- ✅ Systém skórování s ukládáním nejlepšího skóre
- ✅ Pauza hry (SPACE)
- ✅ Restart a start obrazovky
- ✅ Postupné zrychlování hry
- ✅ Detekce všech kolizí
- ✅ Moderní UI s animacemi

### Jak spustit hru:
1. Otevřete soubor `index.html` v prohlížeči
2. Klikněte na "Začít hru"
3. Ovládejte hada šipkami nebo WASD
4. Sbírejte červené jídlo a vyhněte se kolizím!

Hra je připravena k použití! 🎮🐍
