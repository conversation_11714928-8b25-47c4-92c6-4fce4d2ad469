# PowerShell script pro vytvoření Excel tabulky pro výpočet čisté mzdy

# Vytvoření nové instance Excel aplikace
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $true
$workbook = $excel.Workbooks.Add()
$sheet = $workbook.Worksheets.Item(1)
$sheet.Name = "Výpočet čisté mzdy"

# Nastavení šířky sloupců
$sheet.Columns.Item("A").ColumnWidth = 30
$sheet.Columns.Item("B").ColumnWidth = 15
$sheet.Columns.Item("C").ColumnWidth = 15
$sheet.Columns.Item("D").ColumnWidth = 30

# Nadpis
$sheet.Range("A1").Value2 = "VÝPOČET ČISTÉ MZDY 2025"
$sheet.Range("A1").Font.Bold = $true
$sheet.Range("A1").Font.Size = 16
$sheet.Range("A1:D1").Merge()
$sheet.Range("A1").HorizontalAlignment = -4108 # xlCenter

# Sekce vstupních údajů
$sheet.Range("A3").Value2 = "VSTUPNÍ ÚDAJE"
$sheet.Range("A3").Font.Bold = $true
$sheet.Range("A3").Font.Size = 12
$sheet.Range("A3:B3").Interior.ColorIndex = 37 # Světle modrá

# Vstupní pole
$sheet.Range("A4").Value2 = "Hrubá mzda:"
$sheet.Range("A5").Value2 = "Počet dětí:"
$sheet.Range("A6").Value2 = "Invalidita:"
$sheet.Range("A7").Value2 = "Student:"
$sheet.Range("A8").Value2 = "Podepsané prohlášení k dani:"

# Hodnoty a formátování vstupních polí
$sheet.Range("B4").Value2 = 30000
$sheet.Range("B4").NumberFormat = "# ##0 Kč"
$sheet.Range("B4:B8").Interior.ColorIndex = 36 # Světle žlutá - vstupní pole

# Vytvoření rozbalovacího seznamu pro počet dětí
$sheet.Range("B5").Value2 = 0
$validation1 = $sheet.Range("B5").Validation
$validation1.Add(3, 1, 1, "0,1,2,3,4,5")
$validation1.IgnoreBlank = $true
$validation1.InCellDropdown = $true

# Vytvoření rozbalovacího seznamu pro invaliditu
$sheet.Range("B6").Value2 = "bez"
$validation2 = $sheet.Range("B6").Validation
$validation2.Add(3, 1, 1, "bez,1. stupeň,2. stupeň,3. stupeň")
$validation2.IgnoreBlank = $true
$validation2.InCellDropdown = $true

# Vytvoření rozbalovacího seznamu pro status studenta
$sheet.Range("B7").Value2 = "ne"
$validation3 = $sheet.Range("B7").Validation
$validation3.Add(3, 1, 1, "ano,ne")
$validation3.IgnoreBlank = $true
$validation3.InCellDropdown = $true

# Vytvoření rozbalovacího seznamu pro daňové prohlášení
$sheet.Range("B8").Value2 = "ano"
$validation4 = $sheet.Range("B8").Validation
$validation4.Add(3, 1, 1, "ano,ne")
$validation4.IgnoreBlank = $true
$validation4.InCellDropdown = $true

# Sekce konstant
$sheet.Range("A10").Value2 = "KONSTANTY PRO VÝPOČET"
$sheet.Range("A10").Font.Bold = $true
$sheet.Range("A10").Font.Size = 12
$sheet.Range("A10:B10").Interior.ColorIndex = 37 # Světle modrá

# Hodnoty konstant
$sheet.Range("A11").Value2 = "Sociální pojištění - zaměstnanec:"
$sheet.Range("B11").Value2 = "7.1%"
$sheet.Range("A12").Value2 = "Sociální pojištění - zaměstnavatel:"
$sheet.Range("B12").Value2 = "24.8%"
$sheet.Range("A13").Value2 = "Zdravotní pojištění - zaměstnanec:"
$sheet.Range("B13").Value2 = "4.5%"
$sheet.Range("A14").Value2 = "Zdravotní pojištění - zaměstnavatel:"
$sheet.Range("B14").Value2 = "9%"
$sheet.Range("A15").Value2 = "Daň z příjmu:"
$sheet.Range("B15").Value2 = "15%"
$sheet.Range("A16").Value2 = "Sleva na poplatníka:"
$sheet.Range("B16").Value2 = 2570
$sheet.Range("B16").NumberFormat = "# ##0 Kč"

# Daňové zvýhodnění na děti
$sheet.Range("A17").Value2 = "Daňové zvýhodnění - 1. dítě:"
$sheet.Range("B17").Value2 = 1267
$sheet.Range("B17").NumberFormat = "# ##0 Kč"
$sheet.Range("A18").Value2 = "Daňové zvýhodnění - 2. dítě:"
$sheet.Range("B18").Value2 = 1860
$sheet.Range("B18").NumberFormat = "# ##0 Kč"
$sheet.Range("A19").Value2 = "Daňové zvýhodnění - 3. a další dítě:"
$sheet.Range("B19").Value2 = 2320
$sheet.Range("B19").NumberFormat = "# ##0 Kč"

# Slevy na invaliditu
$sheet.Range("A20").Value2 = "Sleva na invaliditu 1. a 2. stupeň:"
$sheet.Range("B20").Value2 = 210
$sheet.Range("B20").NumberFormat = "# ##0 Kč"
$sheet.Range("A21").Value2 = "Sleva na invaliditu 3. stupeň:"
$sheet.Range("B21").Value2 = 420
$sheet.Range("B21").NumberFormat = "# ##0 Kč"

# Sleva na studenta (zrušeno od 2024)
$sheet.Range("A22").Value2 = "Sleva na studenta (zrušeno od 2024):"
$sheet.Range("B22").Value2 = 0
$sheet.Range("B22").NumberFormat = "# ##0 Kč"

# Sekce výpočtů
$sheet.Range("A24").Value2 = "VÝPOČET"
$sheet.Range("A24").Font.Bold = $true
$sheet.Range("A24").Font.Size = 12
$sheet.Range("A24:D24").Interior.ColorIndex = 37 # Světle modrá

# Pole výpočtů
$sheet.Range("A25").Value2 = "Sociální pojištění - zaměstnanec:"
$sheet.Range("A26").Value2 = "Zdravotní pojištění - zaměstnanec:"
$sheet.Range("A27").Value2 = "Sociální pojištění - zaměstnavatel:"
$sheet.Range("A28").Value2 = "Zdravotní pojištění - zaměstnavatel:"
$sheet.Range("A29").Value2 = "Superhrubá mzda:"
$sheet.Range("A30").Value2 = "Základ daně:"
$sheet.Range("A31").Value2 = "Záloha na daň před slevami (15%):"
$sheet.Range("A32").Value2 = "Sleva na poplatníka:"
$sheet.Range("A33").Value2 = "Sleva na invaliditu:"
$sheet.Range("A34").Value2 = "Sleva na studenta:"
$sheet.Range("A35").Value2 = "Daňové zvýhodnění na děti:"
$sheet.Range("A36").Value2 = "Záloha na daň po slevách:"
$sheet.Range("A37").Value2 = "ČISTÁ MZDA:"
$sheet.Range("A37").Font.Bold = $true

# Vzorce pro výpočty
# Sociální pojištění - zaměstnanec (6.5% důchodové + 0.6% nemocenské)
$sheet.Range("B25").Formula = "=B4*0.071"
$sheet.Range("B25").NumberFormat = "# ##0 Kč"

# Zdravotní pojištění - zaměstnanec
$sheet.Range("B26").Formula = "=B4*0.045"
$sheet.Range("B26").NumberFormat = "# ##0 Kč"

# Sociální pojištění - zaměstnavatel
$sheet.Range("B27").Formula = "=B4*0.248"
$sheet.Range("B27").NumberFormat = "# ##0 Kč"

# Zdravotní pojištění - zaměstnavatel
$sheet.Range("B28").Formula = "=B4*0.09"
$sheet.Range("B28").NumberFormat = "# ##0 Kč"

# Superhrubá mzda
$sheet.Range("B29").Formula = "=B4+B27+B28"
$sheet.Range("B29").NumberFormat = "# ##0 Kč"

# Základ daně (bez zaokrouhlení)
$sheet.Range("B30").Formula = "=B29"
$sheet.Range("B30").NumberFormat = "# ##0 Kč"

# Záloha na daň před slevami
$sheet.Range("B31").Formula = "=B30*0.15"
$sheet.Range("B31").NumberFormat = "# ##0 Kč"

# Sleva na poplatníka
$sheet.Range("B32").Formula = "=IF(B8=""ano"",B16,0)"
$sheet.Range("B32").NumberFormat = "# ##0 Kč"

# Sleva na invaliditu
$sheet.Range("B33").Formula = "=IF(OR(B6=""1. stupeň"",B6=""2. stupeň""),B20,IF(B6=""3. stupeň"",B21,0))"
$sheet.Range("B33").NumberFormat = "# ##0 Kč"

# Sleva na studenta (zrušeno od 2024)
$sheet.Range("B34").Formula = "=IF(AND(B7=""ano"",B8=""ano""),B22,0)"
$sheet.Range("B34").NumberFormat = "# ##0 Kč"
$sheet.Range("C34").Formula = "=""(Poznámka: zrušeno od 2024)"""
$sheet.Range("C34").Font.Italic = $true
$sheet.Range("C34").Font.Size = 8

# Daňové zvýhodnění na děti
$sheet.Range("B35").Formula = "=IF(B8=""ano"",IF(B5>=1,B17,0)+IF(B5>=2,B18,0)+IF(B5>=3,(B5-2)*B19,0),0)"
$sheet.Range("B35").NumberFormat = "# ##0 Kč"

# Záloha na daň po slevách
$sheet.Range("B36").Formula = "=MAX(B31-B32-B33-B34-B35,0)"
$sheet.Range("B36").NumberFormat = "# ##0 Kč"

# Čistá mzda
$sheet.Range("B37").Formula = "=B4-B25-B26-B36"
$sheet.Range("B37").NumberFormat = "# ##0 Kč"
$sheet.Range("B37").Font.Bold = $true
$sheet.Range("B37").Interior.ColorIndex = 35 # Světle zelená

# Přidání vysvětlujících komentářů
$sheet.Range("D25").Value2 = "Odvod zaměstnance na sociální pojištění (6.5% důchodové + 0.6% nemocenské)"
$sheet.Range("D26").Value2 = "Odvod zaměstnance na zdravotní pojištění"
$sheet.Range("D27").Value2 = "Odvod zaměstnavatele na sociální pojištění"
$sheet.Range("D28").Value2 = "Odvod zaměstnavatele na zdravotní pojištění"
$sheet.Range("D29").Value2 = "Hrubá mzda + odvody zaměstnavatele"
$sheet.Range("D30").Value2 = "Základ pro výpočet daně (bez zaokrouhlení)"
$sheet.Range("D31").Value2 = "Daň před uplatněním slev"
$sheet.Range("D32").Value2 = "Základní sleva na poplatníka"
$sheet.Range("D33").Value2 = "Sleva na invaliditu dle stupně"
$sheet.Range("D34").Value2 = "Sleva pro studenty (zrušeno od 2024)"
$sheet.Range("D35").Value2 = "Zvýhodnění dle počtu dětí"
$sheet.Range("D36").Value2 = "Výsledná daň po uplatnění slev"
$sheet.Range("D37").Value2 = "Hrubá mzda - odvody - daň"

# Přidání grafu
$sheet.Range("A39").Value2 = "GRAFICKÉ ZNÁZORNĚNÍ"
$sheet.Range("A39").Font.Bold = $true
$sheet.Range("A39").Font.Size = 12
$sheet.Range("A39:D39").Interior.ColorIndex = 37 # Světle modrá

# Vytvoření dat pro graf
$sheet.Range("A40").Value2 = "Čistá mzda"
$sheet.Range("B40").Formula = "=B37"
$sheet.Range("A41").Value2 = "Daň"
$sheet.Range("B41").Formula = "=B36"
$sheet.Range("A42").Value2 = "Sociální pojištění"
$sheet.Range("B42").Formula = "=B25"
$sheet.Range("A43").Value2 = "Zdravotní pojištění"
$sheet.Range("B43").Formula = "=B26"

# Vytvoření výsečového grafu
$chartRange = $sheet.Range("A40:B43")
$charts = $sheet.ChartObjects()
$chartObject = $charts.Add(400, 400, 300, 200)
$chart = $chartObject.Chart
$chart.SetSourceData($chartRange)
$chart.ChartType = 5 # xlPie
$chart.HasTitle = $true
$chart.ChartTitle.Text = "Složky mzdy"

# Přidání poznámky o porovnání s předchozím rokem
$sheet.Range("A45").Value2 = "POZNÁMKA K POROVNÁNÍ S PŘEDCHOZÍM ROKEM:"
$sheet.Range("A45").Font.Bold = $true
$sheet.Range("A46").Value2 = "Pro rok 2025 zůstávají sazby daní a slevy na dani stejné jako v roce 2024."
$sheet.Range("A47").Value2 = "Sleva na studenta (335 Kč měsíčně) byla zrušena již od roku 2024."

# Uložení souboru
$filePath = [System.IO.Path]::Combine($PSScriptRoot, "Vypocet_ciste_mzdy.xlsx")
$workbook.SaveAs($filePath)

Write-Host "Soubor byl vytvořen: $filePath"

# Zavření Excel aplikace
$workbook.Close($true)
$excel.Quit()

# Uvolnění COM objektů
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($sheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()
