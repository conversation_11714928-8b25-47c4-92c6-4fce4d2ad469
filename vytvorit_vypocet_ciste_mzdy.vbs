Option Explicit

Sub CreateSalaryCalculator()
    ' Create a new Excel application
    Dim xlApp As Object
    Dim xlWorkbook As Object
    Dim xlSheet As Object
    Dim chartObj As Object

    Set xlApp = CreateObject("Excel.Application")
    xlApp.Visible = True
    Set xlWorkbook = xlApp.Workbooks.Add
    Set xlSheet = xlWorkbook.Sheets(1)
    xlSheet.Name = "Výpočet čisté mzdy"

    ' Set column widths
    xlSheet.Columns("A:A").ColumnWidth = 30
    xlSheet.Columns("B:B").ColumnWidth = 15
    xlSheet.Columns("C:C").ColumnWidth = 15
    xlSheet.Columns("D:D").ColumnWidth = 30

    ' Title
    xlSheet.Range("A1").Value = "VÝPOČET ČISTÉ MZDY 2025"
    xlSheet.Range("A1").Font.Bold = True
    xlSheet.Range("A1").Font.Size = 16
    xlSheet.Range("A1:D1").Merge
    xlSheet.Range("A1").HorizontalAlignment = -4108 ' xlCenter

    ' Input section header
    xlSheet.Range("A3").Value = "VSTUPNÍ ÚDAJE"
    xlSheet.Range("A3").Font.Bold = True
    xlSheet.Range("A3").Font.Size = 12
    xlSheet.Range("A3:B3").Interior.Color = RGB(217, 225, 242) ' Light blue

    ' Input fields
    xlSheet.Range("A4").Value = "Hrubá mzda:"
    xlSheet.Range("A5").Value = "Počet dětí:"
    xlSheet.Range("A6").Value = "Invalidita:"
    xlSheet.Range("A7").Value = "Student:"
    xlSheet.Range("A8").Value = "Podepsané prohlášení k dani:"

    ' Input values and formatting
    xlSheet.Range("B4").Value = 30000
    xlSheet.Range("B4").NumberFormat = "# ##0 Kč"
    xlSheet.Range("B4:B8").Interior.Color = RGB(255, 242, 204) ' Light yellow - input fields

    ' Create dropdown for number of children
    xlSheet.Range("B5").Value = 0
    xlSheet.Range("B5").Validation.Add Type:=3, AlertStyle:=1, Operator:=1, Formula1:="0,1,2,3,4,5"

    ' Create dropdown for disability
    xlSheet.Range("B6").Value = "bez"
    xlSheet.Range("B6").Validation.Add Type:=3, AlertStyle:=1, Operator:=1, Formula1:="bez,1. stupeň,2. stupeň,3. stupeň"

    ' Create dropdown for student status
    xlSheet.Range("B7").Value = "ne"
    xlSheet.Range("B7").Validation.Add Type:=3, AlertStyle:=1, Operator:=1, Formula1:="ano,ne"

    ' Create dropdown for tax declaration
    xlSheet.Range("B8").Value = "ano"
    xlSheet.Range("B8").Validation.Add Type:=3, AlertStyle:=1, Operator:=1, Formula1:="ano,ne"

    ' Constants section
    xlSheet.Range("A10").Value = "KONSTANTY PRO VÝPOČET"
    xlSheet.Range("A10").Font.Bold = True
    xlSheet.Range("A10").Font.Size = 12
    xlSheet.Range("A10:B10").Interior.Color = RGB(217, 225, 242) ' Light blue

    ' Constants values
    xlSheet.Range("A11").Value = "Sociální pojištění - zaměstnanec:"
    xlSheet.Range("B11").Value = "7.1%"
    xlSheet.Range("A12").Value = "Sociální pojištění - zaměstnavatel:"
    xlSheet.Range("B12").Value = "24.8%"
    xlSheet.Range("A13").Value = "Zdravotní pojištění - zaměstnanec:"
    xlSheet.Range("B13").Value = "4.5%"
    xlSheet.Range("A14").Value = "Zdravotní pojištění - zaměstnavatel:"
    xlSheet.Range("B14").Value = "9%"
    xlSheet.Range("A15").Value = "Daň z příjmu:"
    xlSheet.Range("B15").Value = "15%"
    xlSheet.Range("A16").Value = "Sleva na poplatníka:"
    xlSheet.Range("B16").Value = 2570
    xlSheet.Range("B16").NumberFormat = "# ##0 Kč"

    ' Child tax benefits
    xlSheet.Range("A17").Value = "Daňové zvýhodnění - 1. dítě:"
    xlSheet.Range("B17").Value = 1267
    xlSheet.Range("B17").NumberFormat = "# ##0 Kč"
    xlSheet.Range("A18").Value = "Daňové zvýhodnění - 2. dítě:"
    xlSheet.Range("B18").Value = 1860
    xlSheet.Range("B18").NumberFormat = "# ##0 Kč"
    xlSheet.Range("A19").Value = "Daňové zvýhodnění - 3. a další dítě:"
    xlSheet.Range("B19").Value = 2320
    xlSheet.Range("B19").NumberFormat = "# ##0 Kč"

    ' Disability tax benefits
    xlSheet.Range("A20").Value = "Sleva na invaliditu 1. a 2. stupeň:"
    xlSheet.Range("B20").Value = 210
    xlSheet.Range("B20").NumberFormat = "# ##0 Kč"
    xlSheet.Range("A21").Value = "Sleva na invaliditu 3. stupeň:"
    xlSheet.Range("B21").Value = 420
    xlSheet.Range("B21").NumberFormat = "# ##0 Kč"

    ' Student tax benefit (note that it's canceled in 2024)
    xlSheet.Range("A22").Value = "Sleva na studenta (zrušeno od 2024):"
    xlSheet.Range("B22").Value = 0
    xlSheet.Range("B22").NumberFormat = "# ##0 Kč"

    ' Calculation section
    xlSheet.Range("A24").Value = "VÝPOČET"
    xlSheet.Range("A24").Font.Bold = True
    xlSheet.Range("A24").Font.Size = 12
    xlSheet.Range("A24:D24").Interior.Color = RGB(217, 225, 242) ' Light blue

    ' Calculation fields
    xlSheet.Range("A25").Value = "Sociální pojištění - zaměstnanec:"
    xlSheet.Range("A26").Value = "Zdravotní pojištění - zaměstnanec:"
    xlSheet.Range("A27").Value = "Sociální pojištění - zaměstnavatel:"
    xlSheet.Range("A28").Value = "Zdravotní pojištění - zaměstnavatel:"
    xlSheet.Range("A29").Value = "Superhrubá mzda:"
    xlSheet.Range("A30").Value = "Základ daně:"
    xlSheet.Range("A31").Value = "Záloha na daň před slevami (15%):"
    xlSheet.Range("A32").Value = "Sleva na poplatníka:"
    xlSheet.Range("A33").Value = "Sleva na invaliditu:"
    xlSheet.Range("A34").Value = "Sleva na studenta:"
    xlSheet.Range("A35").Value = "Daňové zvýhodnění na děti:"
    xlSheet.Range("A36").Value = "Záloha na daň po slevách:"
    xlSheet.Range("A37").Value = "ČISTÁ MZDA:"
    xlSheet.Range("A37").Font.Bold = True

    ' Formulas for calculations
    ' Social insurance - employee (6.5% pension + 0.6% sickness)
    xlSheet.Range("B25").Formula = "=B4*0.071"
    xlSheet.Range("B25").NumberFormat = "# ##0 Kč"

    ' Health insurance - employee
    xlSheet.Range("B26").Formula = "=B4*0.045"
    xlSheet.Range("B26").NumberFormat = "# ##0 Kč"

    ' Social insurance - employer
    xlSheet.Range("B27").Formula = "=B4*0.248"
    xlSheet.Range("B27").NumberFormat = "# ##0 Kč"

    ' Health insurance - employer
    xlSheet.Range("B28").Formula = "=B4*0.09"
    xlSheet.Range("B28").NumberFormat = "# ##0 Kč"

    ' Super gross salary
    xlSheet.Range("B29").Formula = "=B4+B27+B28"
    xlSheet.Range("B29").NumberFormat = "# ##0 Kč"

    ' Tax base (without rounding)
    xlSheet.Range("B30").Formula = "=B29"
    xlSheet.Range("B30").NumberFormat = "# ##0 Kč"

    ' Tax advance before reliefs
    xlSheet.Range("B31").Formula = "=B30*0.15"
    xlSheet.Range("B31").NumberFormat = "# ##0 Kč"

    ' Taxpayer relief
    xlSheet.Range("B32").Formula = "=IF(B8=""ano"",B16,0)"
    xlSheet.Range("B32").NumberFormat = "# ##0 Kč"

    ' Disability relief
    xlSheet.Range("B33").Formula = "=IF(OR(B6=""1. stupeň"",B6=""2. stupeň""),B20,IF(B6=""3. stupeň"",B21,0))"
    xlSheet.Range("B33").NumberFormat = "# ##0 Kč"

    ' Student relief (note that it's canceled in 2024)
    xlSheet.Range("B34").Formula = "=IF(AND(B7=""ano"",B8=""ano""),B22,0)"
    xlSheet.Range("B34").NumberFormat = "# ##0 Kč"
    xlSheet.Range("C34").Formula = "=""(Poznámka: zrušeno od 2024)"""
    xlSheet.Range("C34").Font.Italic = True
    xlSheet.Range("C34").Font.Size = 8

    ' Child tax benefits
    xlSheet.Range("B35").Formula = "=IF(B8=""ano"",IF(B5>=1,B17,0)+IF(B5>=2,B18,0)+IF(B5>=3,(B5-2)*B19,0),0)"
    xlSheet.Range("B35").NumberFormat = "# ##0 Kč"

    ' Tax advance after reliefs
    xlSheet.Range("B36").Formula = "=MAX(B31-B32-B33-B34-B35,0)"
    xlSheet.Range("B36").NumberFormat = "# ##0 Kč"

    ' Net salary
    xlSheet.Range("B37").Formula = "=B4-B25-B26-B36"
    xlSheet.Range("B37").NumberFormat = "# ##0 Kč"
    xlSheet.Range("B37").Font.Bold = True
    xlSheet.Range("B37").Interior.Color = RGB(198, 239, 206) ' Light green

    ' Add explanatory comments
    xlSheet.Range("D25").Value = "Odvod zaměstnance na sociální pojištění (6.5% důchodové + 0.6% nemocenské)"
    xlSheet.Range("D26").Value = "Odvod zaměstnance na zdravotní pojištění"
    xlSheet.Range("D27").Value = "Odvod zaměstnavatele na sociální pojištění"
    xlSheet.Range("D28").Value = "Odvod zaměstnavatele na zdravotní pojištění"
    xlSheet.Range("D29").Value = "Hrubá mzda + odvody zaměstnavatele"
    xlSheet.Range("D30").Value = "Základ pro výpočet daně (bez zaokrouhlení)"
    xlSheet.Range("D31").Value = "Daň před uplatněním slev"
    xlSheet.Range("D32").Value = "Základní sleva na poplatníka"
    xlSheet.Range("D33").Value = "Sleva na invaliditu dle stupně"
    xlSheet.Range("D34").Value = "Sleva pro studenty (zrušeno od 2024)"
    xlSheet.Range("D35").Value = "Zvýhodnění dle počtu dětí"
    xlSheet.Range("D36").Value = "Výsledná daň po uplatnění slev"
    xlSheet.Range("D37").Value = "Hrubá mzda - odvody - daň"

    ' Add chart
    xlSheet.Range("A39").Value = "GRAFICKÉ ZNÁZORNĚNÍ"
    xlSheet.Range("A39").Font.Bold = True
    xlSheet.Range("A39").Font.Size = 12
    xlSheet.Range("A39:D39").Interior.Color = RGB(217, 225, 242) ' Light blue

    ' Create data for chart
    xlSheet.Range("A40").Value = "Čistá mzda"
    xlSheet.Range("B40").Formula = "=B37"
    xlSheet.Range("A41").Value = "Daň"
    xlSheet.Range("B41").Formula = "=B36"
    xlSheet.Range("A42").Value = "Sociální pojištění"
    xlSheet.Range("B42").Formula = "=B25"
    xlSheet.Range("A43").Value = "Zdravotní pojištění"
    xlSheet.Range("B43").Formula = "=B26"

    ' Create pie chart
    Set chartObj = xlSheet.Shapes.AddChart2(201, 5, xlSheet.Range("C40:D48"), 300, 200)
    chartObj.Chart.SetSourceData xlSheet.Range("A40:B43")
    chartObj.Chart.ChartTitle.Text = "Složky mzdy"

    ' Add note about 2024/2025 comparison
    xlSheet.Range("A45").Value = "POZNÁMKA K POROVNÁNÍ S PŘEDCHOZÍM ROKEM:"
    xlSheet.Range("A45").Font.Bold = True
    xlSheet.Range("A46").Value = "Pro rok 2025 zůstávají sazby daní a slevy na dani stejné jako v roce 2024."
    xlSheet.Range("A47").Value = "Sleva na studenta (335 Kč měsíčně) byla zrušena již od roku 2024."

    ' Save the workbook
    xlWorkbook.SaveAs CreateObject("WScript.Shell").SpecialFolders("Desktop") & "\Vypocet_ciste_mzdy.xlsx"

    ' Clean up
    xlWorkbook.Close False
    xlApp.Quit
    Set xlSheet = Nothing
    Set xlWorkbook = Nothing
    Set xlApp = Nothing

    MsgBox "Soubor byl vytvořen na ploše s názvem 'Vypocet_ciste_mzdy.xlsx'", vbInformation, "Hotovo"
End Sub

' Run the main procedure
CreateSalaryCalculator
