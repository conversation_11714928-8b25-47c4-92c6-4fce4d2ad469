// Her<PERSON><PERSON> konstanty
const GRID_SIZE = 20;
const CANVAS_SIZE = 400;
const INITIAL_SPEED = 150;
const SPEED_INCREASE = 5;
const MIN_SPEED = 50;

// Hern<PERSON> proměnné
let canvas, ctx;
let gameRunning = false;
let gamePaused = false;
let gameSpeed = INITIAL_SPEED;
let score = 0;
let highScore = 0;
let gameLoop;

// Had
let snake = [
    { x: 10, y: 10 }
];
let direction = { x: 0, y: 0 };
let nextDirection = { x: 0, y: 0 };

// Jídlo
let food = { x: 15, y: 15 };

// DOM elementy
let startScreen, gameOverScreen, pauseScreen;
let currentScoreEl, highScoreEl, finalScoreEl, newRecordEl;

// Inicializace hry
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
    setupEventListeners();
    loadHighScore();
});

function initializeGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');
    
    // DOM elementy
    startScreen = document.getElementById('start-screen');
    gameOverScreen = document.getElementById('game-over-screen');
    pauseScreen = document.getElementById('pause-screen');
    currentScoreEl = document.getElementById('current-score');
    highScoreEl = document.getElementById('high-score');
    finalScoreEl = document.getElementById('final-score');
    newRecordEl = document.getElementById('new-record');
    
    updateScoreDisplay();
}

function setupEventListeners() {
    // Tlačítka
    document.getElementById('start-btn').addEventListener('click', startGame);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    document.getElementById('resume-btn').addEventListener('click', resumeGame);
    document.getElementById('pause-btn').addEventListener('click', togglePause);
    
    // Klávesnice
    document.addEventListener('keydown', handleKeyPress);
    
    // Mobilní ovládání
    document.getElementById('up-btn').addEventListener('click', () => changeDirection(0, -1));
    document.getElementById('down-btn').addEventListener('click', () => changeDirection(0, 1));
    document.getElementById('left-btn').addEventListener('click', () => changeDirection(-1, 0));
    document.getElementById('right-btn').addEventListener('click', () => changeDirection(1, 0));
}

function handleKeyPress(event) {
    if (!gameRunning && !gamePaused) return;
    
    switch(event.key) {
        case 'ArrowUp':
        case 'w':
        case 'W':
            event.preventDefault();
            changeDirection(0, -1);
            break;
        case 'ArrowDown':
        case 's':
        case 'S':
            event.preventDefault();
            changeDirection(0, 1);
            break;
        case 'ArrowLeft':
        case 'a':
        case 'A':
            event.preventDefault();
            changeDirection(-1, 0);
            break;
        case 'ArrowRight':
        case 'd':
        case 'D':
            event.preventDefault();
            changeDirection(1, 0);
            break;
        case ' ':
            event.preventDefault();
            togglePause();
            break;
    }
}

function changeDirection(x, y) {
    // Zabránit otočení do opačného směru
    if (direction.x !== -x || direction.y !== -y) {
        nextDirection = { x, y };
    }
}

function startGame() {
    resetGame();
    gameRunning = true;
    gamePaused = false;
    startScreen.classList.add('hidden');
    
    // Počáteční směr (doprava)
    direction = { x: 1, y: 0 };
    nextDirection = { x: 1, y: 0 };
    
    gameLoop = setInterval(updateGame, gameSpeed);
}

function restartGame() {
    gameOverScreen.classList.add('hidden');
    startGame();
}

function resetGame() {
    snake = [{ x: 10, y: 10 }];
    direction = { x: 0, y: 0 };
    nextDirection = { x: 0, y: 0 };
    score = 0;
    gameSpeed = INITIAL_SPEED;
    generateFood();
    updateScoreDisplay();
    clearInterval(gameLoop);
}

function updateGame() {
    if (gamePaused) return;
    
    // Aktualizovat směr
    direction = { ...nextDirection };
    
    // Pohyb hada
    const head = { ...snake[0] };
    head.x += direction.x;
    head.y += direction.y;
    
    // Kontrola kolizí se stěnami
    if (head.x < 0 || head.x >= CANVAS_SIZE / GRID_SIZE || 
        head.y < 0 || head.y >= CANVAS_SIZE / GRID_SIZE) {
        gameOver();
        return;
    }
    
    // Kontrola kolize se sebou
    for (let segment of snake) {
        if (head.x === segment.x && head.y === segment.y) {
            gameOver();
            return;
        }
    }
    
    snake.unshift(head);
    
    // Kontrola jídla
    if (head.x === food.x && head.y === food.y) {
        score++;
        updateScoreDisplay();
        generateFood();
        
        // Zvýšení rychlosti
        if (score % 5 === 0 && gameSpeed > MIN_SPEED) {
            gameSpeed = Math.max(MIN_SPEED, gameSpeed - SPEED_INCREASE);
            clearInterval(gameLoop);
            gameLoop = setInterval(updateGame, gameSpeed);
        }
    } else {
        snake.pop();
    }
    
    draw();
}

function generateFood() {
    do {
        food = {
            x: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE)),
            y: Math.floor(Math.random() * (CANVAS_SIZE / GRID_SIZE))
        };
    } while (snake.some(segment => segment.x === food.x && segment.y === food.y));
}

function draw() {
    // Vyčistit canvas
    ctx.fillStyle = '#2d3748';
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);
    
    // Nakreslit hada
    ctx.fillStyle = '#48bb78';
    snake.forEach((segment, index) => {
        if (index === 0) {
            // Hlava hada
            ctx.fillStyle = '#38a169';
        } else {
            ctx.fillStyle = '#48bb78';
        }
        ctx.fillRect(segment.x * GRID_SIZE, segment.y * GRID_SIZE, GRID_SIZE - 2, GRID_SIZE - 2);
    });
    
    // Nakreslit jídlo
    ctx.fillStyle = '#f56565';
    ctx.fillRect(food.x * GRID_SIZE, food.y * GRID_SIZE, GRID_SIZE - 2, GRID_SIZE - 2);
}

function gameOver() {
    gameRunning = false;
    clearInterval(gameLoop);
    
    // Kontrola nového rekordu
    if (score > highScore) {
        highScore = score;
        saveHighScore();
        newRecordEl.classList.remove('hidden');
    } else {
        newRecordEl.classList.add('hidden');
    }
    
    finalScoreEl.textContent = score;
    updateScoreDisplay();
    gameOverScreen.classList.remove('hidden');
}

function togglePause() {
    if (!gameRunning) return;
    
    gamePaused = !gamePaused;
    
    if (gamePaused) {
        pauseScreen.classList.remove('hidden');
    } else {
        pauseScreen.classList.add('hidden');
    }
}

function resumeGame() {
    gamePaused = false;
    pauseScreen.classList.add('hidden');
}

function updateScoreDisplay() {
    currentScoreEl.textContent = score;
    highScoreEl.textContent = highScore;
}

function loadHighScore() {
    const saved = localStorage.getItem('snakeHighScore');
    if (saved) {
        highScore = parseInt(saved);
        updateScoreDisplay();
    }
}

function saveHighScore() {
    localStorage.setItem('snakeHighScore', highScore.toString());
}

// Počáteční vykreslení
generateFood();
draw();
