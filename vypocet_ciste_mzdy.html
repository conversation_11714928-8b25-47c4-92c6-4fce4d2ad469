<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Výpočet čisté m<PERSON>dy 2025</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .section-title {
            background-color: #d9e1f2;
            padding: 5px 10px;
            font-weight: bold;
            margin-bottom: 10px;
            border-radius: 3px;
        }
        .input-section {
            background-color: #f8f9fa;
        }
        .constants-section {
            background-color: #f8f9fa;
        }
        .calculation-section {
            background-color: #f8f9fa;
        }
        .input-field {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 250px;
        }
        input, select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background-color: #fff2cc;
            width: 150px;
        }
        .result-row {
            margin-bottom: 5px;
        }
        .result-label {
            display: inline-block;
            width: 300px;
        }
        .result-value {
            display: inline-block;
            width: 150px;
            text-align: right;
        }
        .explanation {
            display: inline-block;
            margin-left: 20px;
            color: #666;
            font-style: italic;
        }
        .net-salary {
            font-weight: bold;
            background-color: #c6efce;
            padding: 5px;
            border-radius: 3px;
        }
        .chart-container {
            width: 500px;
            height: 300px;
            margin: 20px auto;
        }
        .note {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #3498db;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #d9e1f2;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Výpočet čisté mzdy 2025</h1>

    <div class="section input-section">
        <div class="section-title">VSTUPNÍ ÚDAJE</div>

        <div class="input-field">
            <label for="gross-salary">Hrubá mzda:</label>
            <input type="number" id="gross-salary" value="30000">
        </div>

        <div class="input-field">
            <label for="children">Počet dětí:</label>
            <select id="children">
                <option value="0">0</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
            </select>
        </div>

        <div class="input-field">
            <label for="disability">Invalidita:</label>
            <select id="disability">
                <option value="bez">bez</option>
                <option value="1">1. stupeň</option>
                <option value="2">2. stupeň</option>
                <option value="3">3. stupeň</option>
            </select>
        </div>

        <div class="input-field">
            <label for="student">Student:</label>
            <select id="student">
                <option value="no">ne</option>
                <option value="yes">ano</option>
            </select>
        </div>

        <div class="input-field">
            <label for="tax-declaration">Podepsané prohlášení k dani:</label>
            <select id="tax-declaration">
                <option value="yes">ano</option>
                <option value="no">ne</option>
            </select>
        </div>
    </div>

    <div class="section constants-section">
        <div class="section-title">KONSTANTY PRO VÝPOČET</div>

        <table>
            <tr>
                <th>Položka</th>
                <th>Hodnota</th>
            </tr>
            <tr>
                <td>Sociální pojištění - zaměstnanec</td>
                <td>7.1%</td>
            </tr>
            <tr>
                <td>Sociální pojištění - zaměstnavatel</td>
                <td>24.8%</td>
            </tr>
            <tr>
                <td>Zdravotní pojištění - zaměstnanec</td>
                <td>4.5%</td>
            </tr>
            <tr>
                <td>Zdravotní pojištění - zaměstnavatel</td>
                <td>9%</td>
            </tr>
            <tr>
                <td>Daň z příjmu</td>
                <td>15%</td>
            </tr>
            <tr>
                <td>Sleva na poplatníka</td>
                <td>2 570 Kč</td>
            </tr>
            <tr>
                <td>Daňové zvýhodnění - 1. dítě</td>
                <td>1 267 Kč</td>
            </tr>
            <tr>
                <td>Daňové zvýhodnění - 2. dítě</td>
                <td>1 860 Kč</td>
            </tr>
            <tr>
                <td>Daňové zvýhodnění - 3. a další dítě</td>
                <td>2 320 Kč</td>
            </tr>
            <tr>
                <td>Sleva na invaliditu 1. a 2. stupeň</td>
                <td>210 Kč</td>
            </tr>
            <tr>
                <td>Sleva na invaliditu 3. stupeň</td>
                <td>420 Kč</td>
            </tr>
            <tr>
                <td>Sleva na studenta (zrušeno od 2024)</td>
                <td>0 Kč</td>
            </tr>
        </table>
    </div>

    <div class="section calculation-section">
        <div class="section-title">VÝPOČET</div>

        <div id="calculation-results">
            <!-- Výsledky výpočtu budou vloženy pomocí JavaScriptu -->
        </div>
    </div>

    <div class="section">
        <div class="section-title">GRAFICKÉ ZNÁZORNĚNÍ</div>
        <div class="chart-container">
            <canvas id="salary-chart"></canvas>
        </div>
    </div>

    <div class="note">
        <strong>POZNÁMKA K POROVNÁNÍ S PŘEDCHOZÍM ROKEM:</strong>
        <p>Pro rok 2025 zůstávají sazby daní a slevy na dani stejné jako v roce 2024.</p>
        <p>Sleva na studenta (335 Kč měsíčně) byla zrušena již od roku 2024.</p>
    </div>

    <script>
        // Konstanty
        const SOCIAL_EMPLOYEE = 0.071; // 6.5% důchodové + 0.6% nemocenské
        const SOCIAL_EMPLOYER = 0.248;
        const HEALTH_EMPLOYEE = 0.045;
        const HEALTH_EMPLOYER = 0.09;
        const TAX_RATE = 0.15;
        const TAXPAYER_RELIEF = 2570;
        const CHILD_RELIEF_1 = 1267;
        const CHILD_RELIEF_2 = 1860;
        const CHILD_RELIEF_3_PLUS = 2320;
        const DISABILITY_RELIEF_1_2 = 210;
        const DISABILITY_RELIEF_3 = 420;
        const STUDENT_RELIEF = 0; // Zrušeno od 2024

        // Formátování měny
        function formatCurrency(amount) {
            return new Intl.NumberFormat('cs-CZ', { style: 'currency', currency: 'CZK', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(amount);
        }

        // Výpočet čisté mzdy
        function calculateSalary() {
            // Získání vstupních hodnot
            const grossSalary = parseFloat(document.getElementById('gross-salary').value);
            const children = parseInt(document.getElementById('children').value);
            const disability = document.getElementById('disability').value;
            const student = document.getElementById('student').value;
            const taxDeclaration = document.getElementById('tax-declaration').value;

            // Výpočet odvodů
            const socialEmployee = grossSalary * SOCIAL_EMPLOYEE;
            const healthEmployee = grossSalary * HEALTH_EMPLOYEE;
            const socialEmployer = grossSalary * SOCIAL_EMPLOYER;
            const healthEmployer = grossSalary * HEALTH_EMPLOYER;

            // Superhrubá mzda a základ daně
            const superGrossSalary = grossSalary + socialEmployer + healthEmployer;
            const taxBase = superGrossSalary;

            // Záloha na daň před slevami
            const taxBeforeReliefs = taxBase * TAX_RATE;

            // Slevy na dani
            const taxpayerRelief = taxDeclaration === 'yes' ? TAXPAYER_RELIEF : 0;

            let disabilityRelief = 0;
            if (disability === '1' || disability === '2') {
                disabilityRelief = DISABILITY_RELIEF_1_2;
            } else if (disability === '3') {
                disabilityRelief = DISABILITY_RELIEF_3;
            }

            const studentRelief = (student === 'yes' && taxDeclaration === 'yes') ? STUDENT_RELIEF : 0;

            // Daňové zvýhodnění na děti
            let childRelief = 0;
            if (taxDeclaration === 'yes') {
                if (children >= 1) childRelief += CHILD_RELIEF_1;
                if (children >= 2) childRelief += CHILD_RELIEF_2;
                if (children >= 3) childRelief += (children - 2) * CHILD_RELIEF_3_PLUS;
            }

            // Výsledná záloha na daň
            const finalTax = Math.max(taxBeforeReliefs - taxpayerRelief - disabilityRelief - studentRelief - childRelief, 0);

            // Čistá mzda
            const netSalary = grossSalary - socialEmployee - healthEmployee - finalTax;

            // Zobrazení výsledků
            const resultsHTML = `
                <div class="result-row">
                    <span class="result-label">Sociální pojištění - zaměstnanec:</span>
                    <span class="result-value">${formatCurrency(socialEmployee)}</span>
                    <span class="explanation">Odvod zaměstnance na sociální pojištění</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Zdravotní pojištění - zaměstnanec:</span>
                    <span class="result-value">${formatCurrency(healthEmployee)}</span>
                    <span class="explanation">Odvod zaměstnance na zdravotní pojištění</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Sociální pojištění - zaměstnavatel:</span>
                    <span class="result-value">${formatCurrency(socialEmployer)}</span>
                    <span class="explanation">Odvod zaměstnavatele na sociální pojištění</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Zdravotní pojištění - zaměstnavatel:</span>
                    <span class="result-value">${formatCurrency(healthEmployer)}</span>
                    <span class="explanation">Odvod zaměstnavatele na zdravotní pojištění</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Superhrubá mzda:</span>
                    <span class="result-value">${formatCurrency(superGrossSalary)}</span>
                    <span class="explanation">Hrubá mzda + odvody zaměstnavatele</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Základ daně:</span>
                    <span class="result-value">${formatCurrency(taxBase)}</span>
                    <span class="explanation">Základ pro výpočet daně</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Záloha na daň před slevami (15%):</span>
                    <span class="result-value">${formatCurrency(taxBeforeReliefs)}</span>
                    <span class="explanation">Daň před uplatněním slev</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Sleva na poplatníka:</span>
                    <span class="result-value">${formatCurrency(taxpayerRelief)}</span>
                    <span class="explanation">Základní sleva na poplatníka</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Sleva na invaliditu:</span>
                    <span class="result-value">${formatCurrency(disabilityRelief)}</span>
                    <span class="explanation">Sleva na invaliditu dle stupně</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Sleva na studenta:</span>
                    <span class="result-value">${formatCurrency(studentRelief)}</span>
                    <span class="explanation">Sleva pro studenty (zrušeno od 2024)</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Daňové zvýhodnění na děti:</span>
                    <span class="result-value">${formatCurrency(childRelief)}</span>
                    <span class="explanation">Zvýhodnění dle počtu dětí</span>
                </div>
                <div class="result-row">
                    <span class="result-label">Záloha na daň po slevách:</span>
                    <span class="result-value">${formatCurrency(finalTax)}</span>
                    <span class="explanation">Výsledná daň po uplatnění slev</span>
                </div>
                <div class="result-row net-salary">
                    <span class="result-label">ČISTÁ MZDA:</span>
                    <span class="result-value">${formatCurrency(netSalary)}</span>
                    <span class="explanation">Hrubá mzda - odvody - daň</span>
                </div>
            `;

            document.getElementById('calculation-results').innerHTML = resultsHTML;

            // Aktualizace grafu
            updateChart(netSalary, finalTax, socialEmployee, healthEmployee);
        }

        // Vytvoření grafu
        let salaryChart;

        function updateChart(netSalary, tax, social, health) {
            const ctx = document.getElementById('salary-chart').getContext('2d');

            // Zničení existujícího grafu, pokud existuje
            if (salaryChart) {
                salaryChart.destroy();
            }

            // Vytvoření nového grafu
            salaryChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Čistá mzda', 'Daň', 'Sociální pojištění', 'Zdravotní pojištění'],
                    datasets: [{
                        data: [netSalary, tax, social, health],
                        backgroundColor: [
                            '#c6efce',
                            '#ffeb9c',
                            '#ffc7ce',
                            '#9bc2e6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Složky mzdy'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = Math.round((value / (netSalary + tax + social + health)) * 100);
                                    return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Přidání posluchačů událostí pro vstupní pole
        document.getElementById('gross-salary').addEventListener('input', calculateSalary);
        document.getElementById('children').addEventListener('change', calculateSalary);
        document.getElementById('disability').addEventListener('change', calculateSalary);
        document.getElementById('student').addEventListener('change', calculateSalary);
        document.getElementById('tax-declaration').addEventListener('change', calculateSalary);

        // Počáteční výpočet
        calculateSalary();
    </script>
</body>
</html>
