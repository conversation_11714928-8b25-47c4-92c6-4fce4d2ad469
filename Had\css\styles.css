/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
}

/* Header */
header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    color: #4a5568;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.score-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 1.2em;
    font-weight: bold;
}

.score, .high-score {
    padding: 10px 15px;
    background: #f7fafc;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
}

.score {
    color: #2d3748;
}

.high-score {
    color: #d69e2e;
}

/* Herní plocha */
.game-area {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid #4a5568;
    border-radius: 10px;
    background: #2d3748;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Overlay obrazovky */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    z-index: 10;
}

.overlay-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.overlay-content h2 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #2d3748;
}

.overlay-content p {
    margin-bottom: 10px;
    color: #4a5568;
    font-size: 1.1em;
}

.hidden {
    display: none !important;
}

/* Tlačítka */
.game-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.game-btn:active {
    transform: translateY(0);
}

/* Mobilní ovládání */
.mobile-controls {
    display: none;
    margin-top: 20px;
}

.control-row {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 5px 0;
}

.control-btn {
    width: 60px;
    height: 60px;
    background: #4a5568;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.control-btn:hover {
    background: #2d3748;
    transform: scale(1.05);
}

.control-btn:active {
    transform: scale(0.95);
    background: #1a202c;
}

/* Instrukce */
footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
}

.instructions h3 {
    color: #2d3748;
    margin-bottom: 10px;
}

.instructions p {
    color: #4a5568;
    margin: 5px 0;
    font-size: 0.9em;
}

/* Nový rekord */
#new-record {
    color: #d69e2e;
    font-weight: bold;
    font-size: 1.2em;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responzivní design */
@media (max-width: 600px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .score-container {
        flex-direction: column;
        gap: 10px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }
    
    .mobile-controls {
        display: block;
    }
    
    .overlay-content {
        padding: 20px;
        margin: 10px;
    }
}

@media (max-width: 400px) {
    #gameCanvas {
        max-width: 300px;
    }
    
    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2em;
    }
}
