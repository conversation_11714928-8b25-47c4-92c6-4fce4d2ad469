NÁVOD PRO VYTVOŘENÍ TABULKY VÝPOČTU ČISTÉ MZDY V EXCELU

Tento návod vám pomůže vytvořit Excel tabulku pro výpočet čisté mzdy v České republice podle aktuálních sazeb pro rok 2025.

STRUKTURA TABULKY:
=================

1. VSTUPNÍ ÚDAJE (buňky A3-B8)
------------------------------
A3: "VSTUPNÍ ÚDAJE" (nadpis, tučně, velikost 12, pozadí světle modré)
A4: "Hrubá mzda:"
A5: "Počet dětí:"
A6: "Invalidita:"
A7: "Student:"
A8: "Podepsané prohlášení k dani:"

B4: 30000 (formát: # ##0 Kč) - pozadí světle žluté pro všechny vstupní buňky
B5: 0 (vyt<PERSON><PERSON><PERSON> rozbalovací seznam s hodnotami 0,1,2,3,4,5)
B6: "bez" (vytvořte rozbalovací seznam s hodnotami "bez", "1. stupeň", "2. stupeň", "3. stupeň")
B7: "ne" (vytvořte rozbalovací seznam s hodnotami "ano", "ne")
B8: "ano" (vytvořte rozbalovací seznam s hodnotami "ano", "ne")

2. KONSTANTY PRO VÝPOČET (buňky A10-B22)
----------------------------------------
A10: "KONSTANTY PRO VÝPOČET" (nadpis, tučně, velikost 12, pozadí světle modré)
A11: "Sociální pojištění - zaměstnanec:"
A12: "Sociální pojištění - zaměstnavatel:"
A13: "Zdravotní pojištění - zaměstnanec:"
A14: "Zdravotní pojištění - zaměstnavatel:"
A15: "Daň z příjmu:"
A16: "Sleva na poplatníka:"
A17: "Daňové zvýhodnění - 1. dítě:"
A18: "Daňové zvýhodnění - 2. dítě:"
A19: "Daňové zvýhodnění - 3. a další dítě:"
A20: "Sleva na invaliditu 1. a 2. stupeň:"
A21: "Sleva na invaliditu 3. stupeň:"
A22: "Sleva na studenta (zrušeno od 2024):"

B11: "7.1%"
B12: "24.8%"
B13: "4.5%"
B14: "9%"
B15: "15%"
B16: 2570 (formát: # ##0 Kč)
B17: 1267 (formát: # ##0 Kč)
B18: 1860 (formát: # ##0 Kč)
B19: 2320 (formát: # ##0 Kč)
B20: 210 (formát: # ##0 Kč)
B21: 420 (formát: # ##0 Kč)
B22: 0 (formát: # ##0 Kč)

3. VÝPOČET (buňky A24-B37)
--------------------------
A24: "VÝPOČET" (nadpis, tučně, velikost 12, pozadí světle modré)
A25: "Sociální pojištění - zaměstnanec:"
A26: "Zdravotní pojištění - zaměstnanec:"
A27: "Sociální pojištění - zaměstnavatel:"
A28: "Zdravotní pojištění - zaměstnavatel:"
A29: "Superhrubá mzda:"
A30: "Základ daně:"
A31: "Záloha na daň před slevami (15%):"
A32: "Sleva na poplatníka:"
A33: "Sleva na invaliditu:"
A34: "Sleva na studenta:"
A35: "Daňové zvýhodnění na děti:"
A36: "Záloha na daň po slevách:"
A37: "ČISTÁ MZDA:" (tučně)

VZORCE PRO VÝPOČET:
------------------
B25: =B4*0.071 (formát: # ##0 Kč) - Sociální pojištění zaměstnance (6.5% důchodové + 0.6% nemocenské)
B26: =B4*0.045 (formát: # ##0 Kč) - Zdravotní pojištění zaměstnance
B27: =B4*0.248 (formát: # ##0 Kč) - Sociální pojištění zaměstnavatele
B28: =B4*0.09 (formát: # ##0 Kč) - Zdravotní pojištění zaměstnavatele
B29: =B4+B27+B28 (formát: # ##0 Kč) - Superhrubá mzda
B30: =B29 (formát: # ##0 Kč) - Základ daně bez zaokrouhlení
B31: =B30*0.15 (formát: # ##0 Kč) - Záloha na daň před slevami
B32: =IF(B8="ano",B16,0) (formát: # ##0 Kč) - Sleva na poplatníka
B33: =IF(OR(B6="1. stupeň",B6="2. stupeň"),B20,IF(B6="3. stupeň",B21,0)) (formát: # ##0 Kč) - Sleva na invaliditu
B34: =IF(AND(B7="ano",B8="ano"),B22,0) (formát: # ##0 Kč) - Sleva na studenta
B35: =IF(B8="ano",IF(B5>=1,B17,0)+IF(B5>=2,B18,0)+IF(B5>=3,(B5-2)*B19,0),0) (formát: # ##0 Kč) - Daňové zvýhodnění na děti
B36: =MAX(B31-B32-B33-B34-B35,0) (formát: # ##0 Kč) - Záloha na daň po slevách
B37: =B4-B25-B26-B36 (formát: # ##0 Kč, pozadí světle zelené, tučně) - ČISTÁ MZDA

4. VYSVĚTLIVKY (buňky D25-D37)
-----------------------------
D25: "Odvod zaměstnance na sociální pojištění (6.5% důchodové + 0.6% nemocenské)"
D26: "Odvod zaměstnance na zdravotní pojištění"
D27: "Odvod zaměstnavatele na sociální pojištění"
D28: "Odvod zaměstnavatele na zdravotní pojištění"
D29: "Hrubá mzda + odvody zaměstnavatele"
D30: "Základ pro výpočet daně (bez zaokrouhlení)"
D31: "Daň před uplatněním slev"
D32: "Základní sleva na poplatníka"
D33: "Sleva na invaliditu dle stupně"
D34: "Sleva pro studenty (zrušeno od 2024)"
D35: "Zvýhodnění dle počtu dětí"
D36: "Výsledná daň po uplatnění slev"
D37: "Hrubá mzda - odvody - daň"

5. GRAFICKÉ ZNÁZORNĚNÍ (buňky A39-B43 + graf)
--------------------------------------------
A39: "GRAFICKÉ ZNÁZORNĚNÍ" (nadpis, tučně, velikost 12, pozadí světle modré)
A40: "Čistá mzda"
A41: "Daň"
A42: "Sociální pojištění"
A43: "Zdravotní pojištění"

B40: =B37 - Čistá mzda
B41: =B36 - Daň
B42: =B25 - Sociální pojištění
B43: =B26 - Zdravotní pojištění

Vytvořte výsečový graf z dat v buňkách A40:B43 s názvem "Složky mzdy".

6. POZNÁMKA K POROVNÁNÍ S PŘEDCHOZÍM ROKEM (buňky A45-A47)
---------------------------------------------------------
A45: "POZNÁMKA K POROVNÁNÍ S PŘEDCHOZÍM ROKEM:" (tučně)
A46: "Pro rok 2025 zůstávají sazby daní a slevy na dani stejné jako v roce 2024."
A47: "Sleva na studenta (335 Kč měsíčně) byla zrušena již od roku 2024."

FORMÁTOVÁNÍ:
===========
- Nastavte šířku sloupců: A=30, B=15, C=15, D=30
- Použijte barevné odlišení: vstupní pole (světle žlutá), nadpisy (světle modrá), čistá mzda (světle zelená)
- Formátujte všechny peněžní hodnoty jako měnu (# ##0 Kč)
- Přidejte komentáře vysvětlující výpočty ve sloupci D

POZNÁMKY:
=========
1. Sleva na studenta byla zrušena od roku 2024.
2. V roce 2025 platí zaměstnanec ze své hrubé mzdy na sociální a nemocenské pojištění celkem 7,1 %:
   - 6,5 % na důchodové pojištění
   - 0,6 % na nemocenské pojištění
3. Daňové zvýhodnění na děti pro rok 2025:
   - První dítě: 1,267 Kč měsíčně (15,204 Kč ročně)
   - Druhé dítě: 1,860 Kč měsíčně (22,320 Kč ročně)
   - Třetí a každé další dítě: 2,320 Kč měsíčně (27,840 Kč ročně)
4. Slevy na invaliditu pro rok 2025:
   - 1. a 2. stupeň: 210 Kč měsíčně (2,520 Kč ročně)
   - 3. stupeň: 420 Kč měsíčně (5,040 Kč ročně)
5. Sleva na poplatníka: 2,570 Kč měsíčně (30,840 Kč ročně)
